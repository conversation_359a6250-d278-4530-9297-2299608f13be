import 'package:flutter/material.dart';
import 'package:octasync_client/imports.dart';

/// 职级创建弹窗
class GradeDialog extends StatefulWidget {
  final Widget? child;
  const GradeDialog({super.key, this.child});

  @override
  State<GradeDialog> createState() => GradeDialogState();
}

// 将 State 类改为公开，以便外部可以访问
class GradeDialogState extends State<GradeDialog> {
  /// 是否继续添加下一条
  bool isAddNext = false;
  bool btnLoading = false;

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _nameController = TextEditingController();
  final SelectController<int> _gradeSelectController = SelectController<int>();

  @override
  void initState() {
    super.initState();
    // _gradeSelectController.setOptions(_getOptions());
  }

  _getOptions() {
    return [
      SelectOption(value: 1, label: '管理岗位'),
      SelectOption(value: 2, label: '软/硬件研发'),
      SelectOption(value: 3, label: '营销类'),
      SelectOption(value: 4, label: '行政后勤'),
      SelectOption(value: 5, label: '人力资源'),
      SelectOption(value: 6, label: '财务管理'),
      SelectOption(value: 7, label: '市场调研管理'),
      SelectOption(value: 8, label: '质量管理'),
      SelectOption(value: 9, label: '供应链'),
      SelectOption(value: 10, label: '安装售后'),
    ];
  }

  /// 重置数据
  void resetFormData([StateSetter? setDialogState]) {
    _nameController.text = '';
    _gradeSelectController.clear();
  }

  /// 提交
  createRequest(BuildContext context, [StateSetter? setDialogState]) {
    resetFormData(setDialogState);
  }

  /// 打开添加部门弹窗
  void showGradeDialog(BuildContext context) {
    double labelWidth = 80;

    AppDialog.show(
      width: 480,
      context: context,
      title: '添加部门',
      isDrawer: true,
      slideDirection: SlideDirection.right,
      showFooter: false,
      padding: EdgeInsetsGeometry.zero,
      child: StatefulBuilder(
        builder: (context, setDialogState) {
          return Column(
            children: [
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(10),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        AppInput(
                          label: "名称",
                          required: true,
                          labelWidth: labelWidth,
                          labelPosition: LabelPosition.left,
                          hintText: "名称",
                          size: InputSize.medium,
                          controller: _nameController,
                          maxLength: 30,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return '请输入名称';
                            }
                            return null;
                          },
                          onChanged: (value) {},
                        ),
                        Row(
                          children: [
                            SizedBox(
                              width: labelWidth,
                              child: Row(
                                children: [
                                  Text('职级序列'),
                                  Text(
                                    ' *',
                                    style: TextStyle(fontSize: 20, color: AppColors.error),
                                  ),
                                ],
                              ),
                            ),
                            Expanded(
                              child: AppSelect<int>(
                                placeholder: '请选择',
                                options: _getOptions(),
                                controller: _gradeSelectController,
                                onChanged: (value) {},
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              Divider(),
              Padding(
                padding: const EdgeInsets.all(10),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Checkbox(
                      value: isAddNext,
                      onChanged: (value) {
                        setDialogState(() {
                          isAddNext = !isAddNext;
                        });
                      },
                    ),
                    Text('继续新建下一条'),
                    const SizedBox(width: 10),
                    AppButton(
                      text: '取消',
                      type: ButtonType.default_,
                      onPressed: () => context.pop(),
                    ),
                    const SizedBox(width: 10),
                    AppButton(
                      text: '确定',
                      type: ButtonType.primary,
                      onPressed: () => createRequest(context, setDialogState),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    _gradeSelectController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.child ?? SizedBox();
  }
}
