import 'package:flutter/material.dart';
import 'dart:async';
import 'package:jyt_components_package/jyt_components_package.dart';
import 'package:octasync_client/main.dart';

class SelectExamplePage extends StatefulWidget {
  const SelectExamplePage({super.key});

  @override
  State<SelectExamplePage> createState() => _SelectExamplePageState();
}

class _SelectExamplePageState extends State<SelectExamplePage> {
  // 普通单选的值
  String? _basicValue;

  // int类型单选的值
  int? _intBasicValue;

  // 禁用的值
  String? _disabledValue = 'option1';

  // 尺寸演示的值
  String? _sizeValue;

  // 可清空的值
  String? _clearableValue;

  // 多选的值
  List<String> _multipleValues = [];

  // 可搜索过滤的值
  String? _filterableValue;

  // 自定义渲染的值
  String? _customTemplateValue;

  // 折叠标签的值
  List<String> _collapseTagsValues = [];

  // 远程搜索的值
  String? _remoteSearchValue;

  // 远程搜索结果选项
  List<SelectOption<String>> _remoteOptions = [];

  // 构建基础的选项列表
  List<SelectOption<String>> _getBasicOptions() {
    return [
      SelectOption(value: 'option1', label: '选项一'),
      SelectOption(value: 'option2', label: '选项二'),
      SelectOption(value: 'option3', label: '选项三'),
      SelectOption(value: 'option4', label: '选项四'),
      SelectOption(value: 'option5', label: '选项五'),
    ];
  }

  // 构建基础的选项列表(int类型)
  List<SelectOption<int>> _getIntBasicOptions() {
    return [
      SelectOption(value: 1, label: '选项一'),
      SelectOption(value: 2, label: '选项二'),
      SelectOption(value: 3, label: '选项三'),
      SelectOption(value: 4, label: '选项四'),
      SelectOption(value: 5, label: '选项五'),
    ];
  }

  // 构建带禁用项的选项列表
  List<SelectOption<String>> _getDisabledOptions() {
    return [
      SelectOption(value: 'option1', label: '选项一'),
      SelectOption(value: 'option2', label: '选项二', disabled: true),
      SelectOption(value: 'option3', label: '选项三'),
      SelectOption(value: 'option4', label: '选项四', disabled: true),
      SelectOption(value: 'option5', label: '选项五'),
    ];
  }

  // 构建带图标的选项列表
  List<SelectOption<String>> _getIconOptions() {
    return [
      SelectOption(value: 'option1', label: '备选项1', iconData: Icons.edit),
      SelectOption(value: 'option2', label: '备选项2', iconData: Icons.delete),
      SelectOption(value: 'option3', label: '备选项3', iconData: Icons.settings),
      SelectOption(value: 'option4', label: '备选项4', iconData: Icons.info),
      SelectOption(value: 'option5', label: '备选项5', iconData: Icons.star),
    ];
  }

  // 构建带分组的选项列表
  List<SelectOption<String>> _getCityOptions() {
    return [
      SelectOption(value: 'beijing', label: '北京', group: '华北'),
      SelectOption(value: 'shanghai', label: '上海', group: '华东'),
      SelectOption(value: 'nanjing', label: '南京', group: '华东'),
      SelectOption(value: 'hangzhou', label: '杭州', group: '华东'),
      SelectOption(value: 'guangzhou', label: '广州', group: '华南'),
      SelectOption(value: 'shenzhen', label: '深圳', group: '华南'),
      SelectOption(value: 'chengdu', label: '成都', group: '西南'),
      SelectOption(value: 'wuhan', label: '武汉', group: '华中'),
    ];
  }

  // 构建模拟远程城市数据源(更多城市)
  List<SelectOption<String>> _getRemoteCityDataSource() {
    return [
      SelectOption(value: 'beijing', label: '北京', group: '华北'),
      SelectOption(value: 'tianjin', label: '天津', group: '华北'),
      SelectOption(value: 'shijiazhuang', label: '石家庄', group: '华北'),
      SelectOption(value: 'tangshan', label: '唐山', group: '华北'),
      SelectOption(value: 'shanghai', label: '上海', group: '华东'),
      SelectOption(value: 'nanjing', label: '南京', group: '华东'),
      SelectOption(value: 'hangzhou', label: '杭州', group: '华东'),
      SelectOption(value: 'suzhou', label: '苏州', group: '华东'),
      SelectOption(value: 'ningbo', label: '宁波', group: '华东'),
      SelectOption(value: 'wuxi', label: '无锡', group: '华东'),
      SelectOption(value: 'guangzhou', label: '广州', group: '华南'),
      SelectOption(value: 'shenzhen', label: '深圳', group: '华南'),
      SelectOption(value: 'zhuhai', label: '珠海', group: '华南'),
      SelectOption(value: 'dongguan', label: '东莞', group: '华南'),
      SelectOption(value: 'foshan', label: '佛山', group: '华南'),
      SelectOption(value: 'chengdu', label: '成都', group: '西南'),
      SelectOption(value: 'chongqing', label: '重庆', group: '西南'),
      SelectOption(value: 'kunming', label: '昆明', group: '西南'),
      SelectOption(value: 'guiyang', label: '贵阳', group: '西南'),
      SelectOption(value: 'wuhan', label: '武汉', group: '华中'),
      SelectOption(value: 'changsha', label: '长沙', group: '华中'),
      SelectOption(value: 'zhengzhou', label: '郑州', group: '华中'),
      SelectOption(value: 'nanchang', label: '南昌', group: '华中'),
      SelectOption(value: 'shenyang', label: '沈阳', group: '东北'),
      SelectOption(value: 'dalian', label: '大连', group: '东北'),
      SelectOption(value: 'harbin', label: '哈尔滨', group: '东北'),
      SelectOption(value: 'changchun', label: '长春', group: '东北'),
      SelectOption(value: 'xian', label: '西安', group: '西北'),
      SelectOption(value: 'lanzhou', label: '兰州', group: '西北'),
      SelectOption(value: 'urumqi', label: '乌鲁木齐', group: '西北'),
      SelectOption(value: 'xining', label: '西宁', group: '西北'),
    ];
  }

  @override
  void dispose() {
    // 确保页面销毁时取消定时器
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Select 选择器')),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildTitle('基础用法'),
            _buildBasicExample(),

            _buildTitle('禁用状态'),
            _buildDisabledExample(),

            _buildTitle('可清空单选'),
            _buildClearableExample(),

            _buildTitle('基础多选'),
            _buildMultipleExample(),

            _buildTitle('折叠标签'),
            _buildCollapseTagsExample(),

            _buildTitle('可搜索'),
            _buildFilterableExample(),

            _buildTitle('远程搜索'),
            _buildRemoteSearchExample(),

            _buildTitle('自定义模板'),
            _buildCustomTemplateExample(),

            _buildTitle('不同尺寸'),
            _buildSizeExample(),
          ],
        ),
      ),
    );
  }

  // 构建基础示例
  Widget _buildBasicExample() {
    return _buildExampleCard(
      description: '适用广泛的基础单选(String类型)和基础单选(int类型)',
      child: Column(
        spacing: 15,
        children: [
          AppSelect<String>(
            placeholder: '请选择String类型',
            options: _getBasicOptions(),
            value: _basicValue,
            onChanged: (value) {
              setState(() {
                _basicValue = value;
              });
            },
          ),
        ],
      ),
    );
  }

  // 构建禁用状态示例
  Widget _buildDisabledExample() {
    return _buildExampleCard(
      description: '禁用整个选择器或某些选项',
      child: Column(
        children: [
          AppSelect<String>(
            placeholder: '请选择',
            options: _getBasicOptions(),
            value: _disabledValue,
            disabled: true,
            onChanged: (value) {
              setState(() {
                _disabledValue = value;
              });
            },
          ),
          const SizedBox(height: 16),
          AppSelect<String>(
            placeholder: '有禁用选项',
            options: _getDisabledOptions(),
            value: _disabledValue,
            onChanged: (value) {
              setState(() {
                _disabledValue = value;
              });
            },
          ),
        ],
      ),
    );
  }

  // 构建可清空示例
  Widget _buildClearableExample() {
    return _buildExampleCard(
      description: '包含清空按钮，可将选择器清空为初始状态',
      child: AppSelect<String>(
        placeholder: '请选择',
        options: _getBasicOptions(),
        value: _clearableValue,
        clearable: true,
        onChanged: (value) {
          setState(() {
            _clearableValue = value;
          });
        },
      ),
    );
  }

  // 构建多选示例
  Widget _buildMultipleExample() {
    return _buildExampleCard(
      description: '适用性较广的基础多选，用 Tag 展示已选项',
      child: AppSelect<String>(
        placeholder: '请选择',
        options: _getBasicOptions(),
        values: _multipleValues,
        multiple: true,
        clearable: true,
        onMultiChanged: (values) {
          setState(() {
            _multipleValues = values;
          });
        },
      ),
    );
  }

  // 构建折叠标签示例
  Widget _buildCollapseTagsExample() {
    return _buildExampleCard(
      description: '多选时，选中项过多可折叠起来显示',
      child: AppSelect<String>(
        placeholder: '请选择',
        options: _getBasicOptions(),
        values: _collapseTagsValues,
        multiple: true,
        collapseTags: true,
        maxCollapseTagCount: 1,
        clearable: true,
        onMultiChanged: (values) {
          setState(() {
            _collapseTagsValues = values;
          });
        },
      ),
    );
  }

  // 构建可搜索示例
  Widget _buildFilterableExample() {
    return _buildExampleCard(
      description: '输入搜索关键字过滤选项',
      child: AppSelect<String>(
        placeholder: '请选择',
        options: _getCityOptions(),
        value: _filterableValue,
        filterable: true,
        clearable: true,
        onChanged: (value) {
          setState(() {
            _filterableValue = value;
          });
        },
      ),
    );
  }

  // 构建自定义模板示例
  Widget _buildCustomTemplateExample() {
    return _buildExampleCard(
      description: '自定义选项和选中标签的渲染',
      child: AppSelect<String>(
        placeholder: '请选择',
        options: _getIconOptions(),
        value: _customTemplateValue,
        clearable: true,
        optionBuilder: (context, option, selected) {
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            child: Row(
              children: [
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: selected ? AppColors.primary : AppColors.background200,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Icon(
                    option.iconData,
                    size: 14,
                    color: selected ? Colors.white : AppColors.icon300,
                  ),
                ),
                const SizedBox(width: 10),
                Text(
                  option.label,
                  style: TextStyle(
                    fontWeight: selected ? FontWeight.bold : FontWeight.normal,
                    color: selected ? AppColors.primary : AppColors.textPrimary,
                  ),
                ),
              ],
            ),
          );
        },
        onChanged: (value) {
          setState(() {
            _customTemplateValue = value;
          });
        },
      ),
    );
  }

  // 构建尺寸示例
  Widget _buildSizeExample() {
    return _buildExampleCard(
      description: '不同尺寸的选择器',
      child: Column(
        children: [
          AppSelect<String>(
            placeholder: '大号尺寸',
            options: _getBasicOptions(),
            value: _sizeValue,
            clearable: true,
            size: SelectSize.large,
            onChanged: (value) {
              setState(() {
                _sizeValue = value;
              });
            },
          ),
          const SizedBox(height: 16),
          AppSelect<String>(
            placeholder: '默认尺寸',
            options: _getBasicOptions(),
            value: _sizeValue,
            clearable: true,
            onChanged: (value) {
              setState(() {
                _sizeValue = value;
              });
            },
          ),
          const SizedBox(height: 16),
          AppSelect<String>(
            placeholder: '小号尺寸',
            options: _getBasicOptions(),
            value: _sizeValue,
            clearable: true,
            size: SelectSize.small,
            onChanged: (value) {
              setState(() {
                _sizeValue = value;
              });
            },
          ),
          const SizedBox(height: 16),
          AppSelect<String>(
            placeholder: '迷你尺寸',
            options: _getBasicOptions(),
            value: _sizeValue,
            clearable: true,
            size: SelectSize.mini,
            onChanged: (value) {
              setState(() {
                _sizeValue = value;
              });
            },
          ),
        ],
      ),
    );
  }

  // 构建远程搜索示例
  Widget _buildRemoteSearchExample() {
    return _buildExampleCard(
      description: '远程搜索，输入关键字时从服务端加载数据',
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AppSelect<String>(
            placeholder: '请输入城市名称',
            options: _remoteOptions,
            value: _remoteSearchValue,
            clearable: true,
            filterable: true,
            // 使用remoteMethod实现远程搜索
            remoteMethod: (String keyword) async {
              print('keyword: $keyword');

              // 使用Future.delayed模拟网络请求
              return Future.delayed(const Duration(milliseconds: 1000), () {
                // 获取完整数据源
                final allCities = _getRemoteCityDataSource();

                // 根据关键词过滤数据
                final filtered =
                    keyword.isEmpty
                        ? allCities
                        : allCities.where((option) {
                          return option.label.contains(keyword);
                        }).toList();

                return filtered;
              });
            },
            onChanged: (value) {
              setState(() {
                _remoteSearchValue = value;
              });
            },
          ),
        ],
      ),
    );
  }

  // 构建标题
  Widget _buildTitle(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Text(title, style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
    );
  }

  // 构建示例卡片
  Widget _buildExampleCard({required String description, required Widget child}) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: context.backgroundWhite,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: context.border200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(description, style: TextStyle(color: context.textSecondary)),
          const SizedBox(height: 16),
          SizedBox(width: 400, child: child),
        ],
      ),
    );
  }
}
